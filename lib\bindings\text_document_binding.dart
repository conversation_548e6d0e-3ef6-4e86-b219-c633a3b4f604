import 'package:get/get.dart';
import '../controllers/text_document_controller.dart';
import '../controllers/auth_controller.dart';

import '../services/export_services/enhanced_pdf_export_service.dart';

/// رابط المستندات النصية - تم تحويله للعمل مع API
///
/// يضمن تسجيل جميع الخدمات والمتحكمات اللازمة للمستندات النصية
/// تم إزالة جميع المراجع لقاعدة البيانات المحلية
class TextDocumentBinding extends Bindings {
  @override
  void dependencies() {
    // تم إزالة DatabaseHelper - سيتم استخدام API

    // التأكد من وجود متحكم المصادقة وتهيئته بشكل صحيح
    if (!Get.isRegistered<AuthController>()) {
      final authController = AuthController();
      Get.put(authController, permanent: true);
    } else {
      // إعادة تهيئة المتحكم إذا كان موجودًا بالفعل
      final authController = Get.find<AuthController>();
      if (authController.currentUser.value == null) {
        authController.onInit(); // إعادة تهيئة المتحكم
      }
    }

    // تم إزالة TextDocumentRepository - سيتم استخدام API

    // تم إزالة خدمة المستندات النصية - سيتم تطويرها لاحقاً
    // if (!Get.isRegistered<TextDocumentService>()) {
    //   Get.put(TextDocumentService(), permanent: true);
    // }

    // التأكد من وجود خدمة تصدير PDF المحسنة
    if (!Get.isRegistered<EnhancedPdfExportService>()) {
      Get.put(EnhancedPdfExportService(), permanent: true);
    }

    // التأكد من وجود متحكم المستندات النصية
    if (!Get.isRegistered<TextDocumentController>()) {
      Get.put(TextDocumentController(), permanent: true);
    }
  }
}
