import 'user_model.dart';

/// نموذج القسم المتوافق مع ASP.NET Core API
class Department {
  final int id;
  final String name;
  final String? description;
  final int? managerId;
  final bool isActive;
  final int createdAt; // Unix timestamp

  // Navigation properties
  final User? manager;

  const Department({
    required this.id,
    required this.name,
    this.description,
    this.managerId,
    this.isActive = true,
    required this.createdAt,
    this.manager,
  });

  factory Department.fromJson(Map<String, dynamic> json) {
    return Department(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      managerId: json['managerId'] as int?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] as int,
      manager: json['manager'] != null 
          ? User.fromJson(json['manager'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'managerId': managerId,
      'isActive': isActive,
      'createdAt': createdAt,
    };
  }

  Department copyWith({
    int? id,
    String? name,
    String? description,
    int? managerId,
    bool? isActive,
    int? createdAt,
    User? manager,
  }) {
    return Department(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      managerId: managerId ?? this.managerId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      manager: manager ?? this.manager,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  @override
  String toString() {
    return 'Department(id: $id, name: $name, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Department && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
