import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../controllers/text_document_controller.dart';
import '../services/auth_service.dart';

import '../services/export_services/enhanced_pdf_export_service.dart';
// تم إزالة استيرادات قاعدة البيانات

/// رابط التطبيق الرئيسي
///
/// يضمن تسجيل جميع الخدمات والمتحكمات الأساسية عند بدء التطبيق
class AppBindings extends Bindings {
  @override
  void dependencies() {
    // تسجيل خدمة المصادقة
    if (!Get.isRegistered<AuthService>()) {
      Get.put(AuthService(), permanent: true);
    }

    // تسجيل متحكم المصادقة
    if (!Get.isRegistered<AuthController>()) {
      Get.put(AuthController(), permanent: true);
    }

    // تم إزالة تسجيل قاعدة البيانات والمستودعات

    // تم إزالة خدمة المستندات النصية - سيتم تطويرها لاحقاً
    // if (!Get.isRegistered<TextDocumentService>()) {
    //   Get.put(TextDocumentService(), permanent: true);
    // }

    // تسجيل خدمة تصدير PDF المحسنة
    if (!Get.isRegistered<EnhancedPdfExportService>()) {
      Get.put(EnhancedPdfExportService(), permanent: true);
    }

    // تسجيل متحكم المستندات النصية
    if (!Get.isRegistered<TextDocumentController>()) {
      Get.put(TextDocumentController(), permanent: true);
    }
  }
}
