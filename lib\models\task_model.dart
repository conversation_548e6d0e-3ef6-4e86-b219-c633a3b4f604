import 'user_model.dart';

/// نموذج المهمة المتوافق مع ASP.NET Core API
class Task {
  final int id;
  final String title;
  final String? description;
  final int? taskTypeId;
  final int creatorId;
  final int? assigneeId;
  final int? departmentId;
  final int createdAt; // Unix timestamp
  final int? startDate; // Unix timestamp
  final int? dueDate; // Unix timestamp
  final int? completedAt; // Unix timestamp
  final int status;
  final int priority;
  final int completionPercentage;
  final int? estimatedTime; // بالدقائق
  final int? actualTime; // بالدقائق
  final bool isDeleted;

  // Navigation properties (سيتم تحميلها من API عند الحاجة)
  final User? assignee;
  final User? creator;
  final TaskStatus? statusNavigation;
  final TaskPriority? priorityNavigation;
  final TaskType? taskType;

  const Task({
    required this.id,
    required this.title,
    this.description,
    this.taskTypeId,
    required this.creatorId,
    this.assigneeId,
    this.departmentId,
    required this.createdAt,
    this.startDate,
    this.dueDate,
    this.completedAt,
    required this.status,
    required this.priority,
    this.completionPercentage = 0,
    this.estimatedTime,
    this.actualTime,
    this.isDeleted = false,
    this.assignee,
    this.creator,
    this.statusNavigation,
    this.priorityNavigation,
    this.taskType,
  });

  factory Task.fromJson(Map<String, dynamic> json) {
    return Task(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      taskTypeId: json['taskTypeId'] as int?,
      creatorId: json['creatorId'] as int,
      assigneeId: json['assigneeId'] as int?,
      departmentId: json['departmentId'] as int?,
      createdAt: json['createdAt'] as int,
      startDate: json['startDate'] as int?,
      dueDate: json['dueDate'] as int?,
      completedAt: json['completedAt'] as int?,
      status: json['status'] as int,
      priority: json['priority'] as int,
      completionPercentage: json['completionPercentage'] as int? ?? 0,
      estimatedTime: json['estimatedTime'] as int?,
      actualTime: json['actualTime'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      assignee: json['assignee'] != null 
          ? User.fromJson(json['assignee'] as Map<String, dynamic>)
          : null,
      creator: json['creator'] != null 
          ? User.fromJson(json['creator'] as Map<String, dynamic>)
          : null,
      statusNavigation: json['statusNavigation'] != null 
          ? TaskStatus.fromJson(json['statusNavigation'] as Map<String, dynamic>)
          : null,
      priorityNavigation: json['priorityNavigation'] != null 
          ? TaskPriority.fromJson(json['priorityNavigation'] as Map<String, dynamic>)
          : null,
      taskType: json['taskType'] != null 
          ? TaskType.fromJson(json['taskType'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'taskTypeId': taskTypeId,
      'creatorId': creatorId,
      'assigneeId': assigneeId,
      'departmentId': departmentId,
      'createdAt': createdAt,
      'startDate': startDate,
      'dueDate': dueDate,
      'completedAt': completedAt,
      'status': status,
      'priority': priority,
      'completionPercentage': completionPercentage,
      'estimatedTime': estimatedTime,
      'actualTime': actualTime,
      'isDeleted': isDeleted,
    };
  }

  Task copyWith({
    int? id,
    String? title,
    String? description,
    int? taskTypeId,
    int? creatorId,
    int? assigneeId,
    int? departmentId,
    int? createdAt,
    int? startDate,
    int? dueDate,
    int? completedAt,
    int? status,
    int? priority,
    int? completionPercentage,
    int? estimatedTime,
    int? actualTime,
    bool? isDeleted,
    User? assignee,
    User? creator,
    TaskStatus? statusNavigation,
    TaskPriority? priorityNavigation,
    TaskType? taskType,
  }) {
    return Task(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      taskTypeId: taskTypeId ?? this.taskTypeId,
      creatorId: creatorId ?? this.creatorId,
      assigneeId: assigneeId ?? this.assigneeId,
      departmentId: departmentId ?? this.departmentId,
      createdAt: createdAt ?? this.createdAt,
      startDate: startDate ?? this.startDate,
      dueDate: dueDate ?? this.dueDate,
      completedAt: completedAt ?? this.completedAt,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      completionPercentage: completionPercentage ?? this.completionPercentage,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      actualTime: actualTime ?? this.actualTime,
      isDeleted: isDeleted ?? this.isDeleted,
      assignee: assignee ?? this.assignee,
      creator: creator ?? this.creator,
      statusNavigation: statusNavigation ?? this.statusNavigation,
      priorityNavigation: priorityNavigation ?? this.priorityNavigation,
      taskType: taskType ?? this.taskType,
    );
  }

  /// التحقق من كون المهمة مكتملة
  bool get isCompleted => completionPercentage >= 100;

  /// التحقق من كون المهمة متأخرة
  bool get isOverdue {
    if (dueDate == null || isCompleted) return false;
    final due = DateTime.fromMillisecondsSinceEpoch(dueDate! * 1000);
    return DateTime.now().isAfter(due);
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ البداية كـ DateTime
  DateTime? get startDateTime => startDate != null 
      ? DateTime.fromMillisecondsSinceEpoch(startDate! * 1000)
      : null;

  /// الحصول على تاريخ الاستحقاق كـ DateTime
  DateTime? get dueDateTime => dueDate != null 
      ? DateTime.fromMillisecondsSinceEpoch(dueDate! * 1000)
      : null;

  /// الحصول على تاريخ الإكمال كـ DateTime
  DateTime? get completedAtDateTime => completedAt != null 
      ? DateTime.fromMillisecondsSinceEpoch(completedAt! * 1000)
      : null;

  @override
  String toString() {
    return 'Task(id: $id, title: $title, status: $status, priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Task && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج حالة المهمة
class TaskStatus {
  final int id;
  final String name;
  final String? description;
  final String? color;
  final int order;
  final bool isDefault;
  final bool isCompleted;

  const TaskStatus({
    required this.id,
    required this.name,
    this.description,
    this.color,
    required this.order,
    this.isDefault = false,
    this.isCompleted = false,
  });

  factory TaskStatus.fromJson(Map<String, dynamic> json) {
    return TaskStatus(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      color: json['color'] as String?,
      order: json['order'] as int,
      isDefault: json['isDefault'] as bool? ?? false,
      isCompleted: json['isCompleted'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color,
      'order': order,
      'isDefault': isDefault,
      'isCompleted': isCompleted,
    };
  }
}

/// نموذج أولوية المهمة
class TaskPriority {
  final int id;
  final String name;
  final String? description;
  final String? color;
  final int level;
  final bool isDefault;

  const TaskPriority({
    required this.id,
    required this.name,
    this.description,
    this.color,
    required this.level,
    this.isDefault = false,
  });

  factory TaskPriority.fromJson(Map<String, dynamic> json) {
    return TaskPriority(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      color: json['color'] as String?,
      level: json['level'] as int,
      isDefault: json['isDefault'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color,
      'level': level,
      'isDefault': isDefault,
    };
  }
}

/// نموذج نوع المهمة
class TaskType {
  final int id;
  final String name;
  final String? description;
  final String? color;
  final String? icon;
  final bool isDefault;

  const TaskType({
    required this.id,
    required this.name,
    this.description,
    this.color,
    this.icon,
    this.isDefault = false,
  });

  factory TaskType.fromJson(Map<String, dynamic> json) {
    return TaskType(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      color: json['color'] as String?,
      icon: json['icon'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color,
      'icon': icon,
      'isDefault': isDefault,
    };
  }
}
